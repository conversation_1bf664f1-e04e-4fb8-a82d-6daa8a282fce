import { shallowRef, onMounted } from 'vue';
import { defineStore } from 'pinia';
import type { UserInfo } from '@/types';
import { Utils } from '@/script';

const defaultUser: UserInfo = {
  user_name: '',
  name: '',
  id: 0,
  user_type: 2,
  password: '',
  permission: 2,
};

export const useUserStore = defineStore('user', () => {
  const userInfo = shallowRef<UserInfo>({ ...defaultUser });

  onMounted(() => {
    const localUser = Utils.getLocal<UserInfo>('user');
    if (localUser) {
      userInfo.value = localUser;
    }
  });

  const setUserInfo = (info?: UserInfo) => {
    if (info) {
      userInfo.value = info;
    } else {
      clearUserInfo();
    }
    Utils.setLocal('user', info);
  };

  const clearUserInfo = () => {
    userInfo.value = { ...defaultUser };
  };

  return {
    userInfo,
    setUserInfo,
    clearUserInfo,
  };
});
