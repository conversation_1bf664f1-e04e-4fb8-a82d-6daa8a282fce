import http from './http';
import type { UserInfo } from '@/types/user';

class UserService {
  private static base = '/user/info';

  static async update(data: UserInfo) {
    const md5 = await import('md5');
    data.password = md5.default(data.password);
    return http<UserInfo>(this.base, {
      method: 'PUT',
      data,
    });
  }

  static async create(data: Omit<UserInfo, 'id'>) {
    const md5 = await import('md5');
    data.password = md5.default(data.password);
    return http<UserInfo>(this.base, {
      method: 'POST',
      data,
    });
  }

  static delete(user_id: number) {
    return http<void>(this.base, {
      method: 'DELETE',
      params: {
        user_id,
      },
    });
  }

  static get() {
    return http<UserInfo[]>(this.base, {
      method: 'GET',
    });
  }

  static async login(data: Pick<UserInfo, 'user_name' | 'password'>) {
    const md5 = await import('md5');
    data.password = md5.default(data.password);
    return http<UserInfo>('/user/login', {
      method: 'POST',
      data,
    });
  }
}

export default UserService;
