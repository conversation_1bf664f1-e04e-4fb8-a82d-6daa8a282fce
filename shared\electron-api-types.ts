import { ipc<PERSON>enderer } from 'electron';

export interface IpcRenderer {
  on: typeof ipcRenderer.on;
  emit: typeof ipcRenderer.emit;
  off: typeof ipcRenderer.off;
  send: typeof ipcRenderer.send;
  invoke: typeof ipcRenderer.invoke;
}

// 日志写入参数接口
export interface WriteLogParams {
  message: string;
  maxFileSize?: number;
  maxFiles?: number;
}

// 执行股票程序返回结果接口
export interface ExecuteStockProgramResult {
  success: boolean;
  error?: string;
}
