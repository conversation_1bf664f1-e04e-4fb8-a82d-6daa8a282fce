import { ipcMain, app } from 'electron';
import { execFileSync } from 'child_process';
import path from 'path';
import type { WriteLogParams } from '../../shared/electron-api-types';
import { LogFileManager } from './log-manager';
import WindowManager from './window-manager';
import { createLogger } from '../../src/libs/logger';

const logger = createLogger('IPCHandlers');

export class IPCHandlers {
  private logFileManager: LogFileManager;
  private windowManager: WindowManager;

  constructor(logFileManager: LogFileManager, windowManager: WindowManager) {
    this.logFileManager = logFileManager;
    this.windowManager = windowManager;
    this.setupHandlers();
  }

  private setupHandlers(): void {
    this.setupWindowHandlers();
    this.setupLogHandlers();
    this.setupDataSyncHandlers();
    this.setupStockHandlers();
  }

  private setupWindowHandlers(): void {
    // 主窗口控制
    ipcMain.on('window-minimize', () => {
      this.windowManager.minimizeMainWindow();
    });

    ipcMain.on('window-close', () => {
      this.windowManager.closeMainWindow();
      // 主窗口关闭时，悬浮窗口也会在 closeMainWindow 中被关闭
    });

    ipcMain.on('window-maximize', (_, maximize?: boolean) => {
      this.windowManager.maximizeMainWindow(maximize);
    });
  }

  private setupLogHandlers(): void {
    // 日志写入处理
    ipcMain.handle('write-log', async (_, params: WriteLogParams) => {
      try {
        await this.logFileManager.writeLog(params);
        return { success: true };
      } catch (error) {
        logger.error('处理日志写入请求失败', error);
        return {
          success: false,
          error: error instanceof Error ? error.message : String(error),
        };
      }
    });
  }

  private setupDataSyncHandlers(): void {
    // 用户登录状态变化
    ipcMain.on('user-login-status-changed', (_, isLoggedIn: boolean) => {
      this.windowManager.setUserLoggedIn(isLoggedIn);
      logger.info('用户登录状态变化', { isLoggedIn });
    });
  }

  private setupStockHandlers(): void {
    // 执行股票程序
    ipcMain.handle('execute-stock-program', async (_, stockCode: string) => {
      try {
        // 获取应用根目录
        const appRoot = process.env.APP_ROOT || process.cwd();

        const getExePath = () => {
          if (!app.isPackaged) {
            return path.join(appRoot, 'resources', 'set_stock.exe');
          } else {
            return path.join(process.resourcesPath, 'extraResources', 'set_stock.exe');
          }
        };

        logger.info('点击股票：', stockCode);

        // 执行外部程序
        await execFileSync(getExePath(), [stockCode]);
        return { success: true };
      } catch (error) {
        logger.error('点击股票失败', error);
        return {
          success: false,
          error: error instanceof Error ? error.message : String(error),
        };
      }
    });
  }

  /**
   * 清理所有IPC处理器
   */
  cleanup(): void {
    // 移除所有IPC监听器
    ipcMain.removeAllListeners('window-minimize');
    ipcMain.removeAllListeners('window-close');
    ipcMain.removeAllListeners('window-maximize');
    ipcMain.removeAllListeners('user-login-status-changed');
    ipcMain.removeAllListeners('execute-stock-program');
    logger.info('IPC处理器清理完成');
  }
}

export default IPCHandlers;
