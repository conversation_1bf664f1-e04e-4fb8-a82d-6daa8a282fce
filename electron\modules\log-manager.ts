import fs from 'node:fs/promises';
import path from 'node:path';
import type { WriteLogParams } from '../../shared/electron-api-types';

export class LogFileManager {
  private logDir: string;
  private currentLogFile: string;
  private currentLogSize = 0;

  constructor() {
    // 使用程序根目录
    this.logDir = './logs';
    this.currentLogFile = path.join(this.logDir, `app-${this.getDateString()}.log`);
    this.ensureLogDir();
  }

  formatTimestamp(): string {
    const timestamp = new Date(Date.now() + 8 * 60 * 60 * 1000)
      .toISOString()
      .replace('T', ' ')
      .replace('Z', '');
    return timestamp;
  }

  private getDateString(): string {
    const now = new Date();
    return now.toISOString().split('T')[0]; // YYYY-MM-DD
  }

  private async ensureLogDir(): Promise<void> {
    try {
      await fs.mkdir(this.logDir, { recursive: true });
    } catch (error) {
      console.error('创建日志目录失败:', error);
    }
  }

  private async getFileSize(filePath: string): Promise<number> {
    try {
      const stats = await fs.stat(filePath);
      return stats.size;
    } catch {
      return 0;
    }
  }

  private async rotateLogFile(maxFileSize: number, maxFiles: number): Promise<void> {
    try {
      const currentSize = await this.getFileSize(this.currentLogFile);
      if (currentSize >= maxFileSize) {
        // 重命名当前文件
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const rotatedFile = path.join(this.logDir, `app-${this.getDateString()}-${timestamp}.log`);
        await fs.rename(this.currentLogFile, rotatedFile);

        // 清理旧文件
        await this.cleanOldLogFiles(maxFiles);
      }
    } catch (error) {
      console.error('日志文件轮转失败:', error);
    }
  }

  private async cleanOldLogFiles(maxFiles: number): Promise<void> {
    try {
      const files = await fs.readdir(this.logDir);
      const logFiles = files
        .filter(file => file.endsWith('.log'))
        .map(file => ({
          name: file,
          path: path.join(this.logDir, file),
        }));

      if (logFiles.length > maxFiles) {
        // 按修改时间排序，删除最旧的文件
        const filesWithStats = await Promise.all(
          logFiles.map(async file => ({
            ...file,
            stats: await fs.stat(file.path),
          })),
        );

        filesWithStats.sort((a, b) => a.stats.mtime.getTime() - b.stats.mtime.getTime());

        const filesToDelete = filesWithStats.slice(0, filesWithStats.length - maxFiles);
        await Promise.all(filesToDelete.map(file => fs.unlink(file.path)));
      }
    } catch (error) {
      console.error('清理旧日志文件失败:', error);
    }
  }

  async writeLog(params: WriteLogParams): Promise<void> {
    try {
      const { message, maxFileSize = 10 * 1024 * 1024, maxFiles = 30 } = params;

      // 检查是否需要轮转日志文件
      await this.rotateLogFile(maxFileSize, maxFiles);

      // 写入日志
      await fs.appendFile(this.currentLogFile, message + '\n', 'utf8');
      this.currentLogSize = await this.getFileSize(this.currentLogFile);
    } catch (error) {
      console.error('写入日志文件失败:', error);
      throw error;
    }
  }
}

export default LogFileManager;
