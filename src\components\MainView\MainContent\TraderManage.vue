<script setup lang="tsx">
import type { ColumnDefinition, RowAction, UserInfo } from '@/types';
import { onMounted, shallowRef } from 'vue';
import { Search } from '@element-plus/icons-vue';
import VirtualizedTable from '@/components/common/VirtualizedTable.vue';
import UserDialog from '@/components/UserDialog.vue';
import { UserService } from '@/api';
import { useFilteredData } from '@/composables/useFilteredData';
import { ElMessage, ElMessageBox } from 'element-plus';

const columns: ColumnDefinition<UserInfo> = [
  {
    key: 'user_name',
    dataKey: 'user_name',
    title: '用户名',
    width: 200,
  },
  {
    key: 'name',
    dataKey: 'name',
    title: '姓名',
    width: 200,
  },
  {
    key: 'permission',
    dataKey: 'permission',
    title: '手动入池权限',
    width: 200,
    cellRenderer: ({ cellData }) => <span>{cellData === 1 ? '是' : '否'}</span>,
  },
];

const rowActions: RowAction<UserInfo>[] = [
  {
    label: '修改',
    onClick: rowData => {
      selected.value = rowData;
      visible.value = true;
    },
    color: 'var(--g-primary)',
  },
  {
    label: '删除',
    onClick: rowData => {
      ElMessageBox.confirm('确定要删除吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(async () => {
        const { error_code, error_msg } = await UserService.delete(rowData.id);
        if (error_code === 0) {
          ElMessage.success('删除成功');
          getTraders();
        } else {
          ElMessage.error(error_msg || '删除失败');
        }
      });
    },
    color: 'var(--g-red)',
  },
];

const traders = shallowRef<UserInfo[]>([]);
const visible = shallowRef(false);
const selected = shallowRef<UserInfo | undefined>();
const { filteredData, query } = useFilteredData(traders, {
  fields: ['user_name'],
});

onMounted(() => {
  getTraders();
});

const getTraders = async () => {
  const { error_code, data } = await UserService.get();
  if (error_code == 0 && data) {
    traders.value = data.filter(x => x.user_type === 2);
  }
};

const handleAdd = () => {
  selected.value = undefined;
  visible.value = true;
};
</script>

<template>
  <div>
    <VirtualizedTable
      :columns="columns"
      :data="filteredData"
      :row-action-width="120"
      :row-actions="rowActions"
    >
      <template #toolbar>
        <el-input
          :prefix-icon="Search"
          class="w-150!"
          placeholder="搜索用户名"
          v-model="query"
          clearable
        ></el-input>
        <div class="actions" flex aic jce>
          <el-button @click="handleAdd" size="small" color="var(--g-primary)">添加</el-button>
        </div>
      </template>
    </VirtualizedTable>
    <UserDialog v-model="visible" :user="selected" @success="getTraders" />
  </div>
</template>

<style scoped></style>
