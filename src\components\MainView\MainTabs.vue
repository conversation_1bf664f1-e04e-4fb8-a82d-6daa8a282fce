<script setup lang="ts">
import { TABS } from '@/enum';
import { Misc } from '@/script';
import { ref } from 'vue';

const activeName = ref<string>('StockPool');

const displayTabs = Misc.isAdmin() ? TABS : TABS.slice(0, 1);

defineExpose({
  activeName,
});
</script>

<template>
  <el-tabs type="card" v-model="activeName">
    <el-tab-pane
      v-for="tab in displayTabs"
      :key="tab.name"
      :label="tab.name"
      :name="tab.component"
    ></el-tab-pane>
  </el-tabs>
</template>

<style scoped></style>
