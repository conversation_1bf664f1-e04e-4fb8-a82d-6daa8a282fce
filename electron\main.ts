import { app } from 'electron';
import { fileURLToPath } from 'node:url';
import path from 'node:path';
import LogFileManager from './modules/log-manager';
import WindowManager from './modules/window-manager';
import IPCHandlers from './modules/ipc-handlers';
import { createLogger } from '../src/libs/logger';

// 环境配置
process.env['ELECTRON_DISABLE_SECURITY_WARNINGS'] = 'true';
const __dirname = path.dirname(fileURLToPath(import.meta.url));
process.env.APP_ROOT = path.join(__dirname, '..');
const RENDERER_DIST = path.join(process.env.APP_ROOT, 'dist/electron');
process.env.VITE_PUBLIC = process.env['VITE_DEV_SERVER_URL']
  ? path.join(process.env.APP_ROOT, 'public')
  : RENDERER_DIST;

const logger = createLogger('ElectronMain');

// 应用实例
let logFileManager: LogFileManager | null = null;
let windowManager: WindowManager | null = null;
let ipcHandlers: IPCHandlers | null = null;

// 初始化应用
async function initializeApp(): Promise<void> {
  try {
    // 创建preload路径
    const preloadPath = path.join(__dirname, 'preload.js');

    // 初始化日志管理器
    logFileManager = new LogFileManager();

    // 初始化窗口管理器
    windowManager = new WindowManager(preloadPath);

    // 初始化IPC处理器
    ipcHandlers = new IPCHandlers(logFileManager, windowManager);

    // 创建主窗口（悬浮窗口在用户登录后创建）
    windowManager.createMainWindow();

    // 记录应用启动日志
    await logFileManager.writeLog({
      message: `[${logFileManager.formatTimestamp()}] [INFO] [ElectronMain] 应用启动完成`,
    });

    logger.info('应用初始化完成');
  } catch (error) {
    logger.error('应用初始化失败', error);
    throw error;
  }
}

// 清理资源
function cleanup(): void {
  try {
    // 清理IPC处理器
    ipcHandlers?.cleanup();

    // 记录应用退出日志
    logFileManager?.writeLog({
      message: `[${logFileManager.formatTimestamp()}] [INFO] [ElectronMain] 应用退出`,
    });

    logger.info('应用清理完成');
  } catch (error) {
    logger.error('应用清理失败', error);
  }
}

// 应用事件处理
app.on('window-all-closed', () => {
  cleanup();
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', () => {
  // On OS X it's common to re-create a window in the app when the
  // dock icon is clicked and there are no other windows open.
  if (app.isReady() && (!windowManager || !windowManager.getMainWindow())) {
    initializeApp().catch(error => {
      logger.error('重新激活应用失败', error);
    });
  }
});

// 应用启动
app.whenReady().then(() => {
  initializeApp().catch(error => {
    logger.error('应用启动失败', error);
    app.quit();
  });
});
