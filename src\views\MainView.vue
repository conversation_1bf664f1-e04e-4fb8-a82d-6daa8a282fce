<script setup lang="ts">
import TopBar from '@/components/MainView/TopBar.vue';
import MainContent from '@/components/MainView/MainContent.vue';
import MainTabs from '@/components/MainView/MainTabs.vue';
import { useTemplateRef } from 'vue';

const mainTabsRef = useTemplateRef('mainTabsRef');
</script>

<template>
  <div h-full flex="~ col" relative>
    <TopBar />
    <MainTabs ref="mainTabsRef" />
    <MainContent flex-1 min-h-1 :active-tab="mainTabsRef?.activeName" />
  </div>
</template>

<style scoped></style>
