import type { PoolStock, QueryInstrumentResponse } from '@/types';
import http from './http';

class PoolService {
  private static base = '/pool';

  static queryInstrument(name: string) {
    return http<QueryInstrumentResponse[]>(`${this.base}/instrument`, {
      params: {
        name,
      },
    });
  }

  static get() {
    return http<PoolStock[]>(`${this.base}/info`);
  }

  static add(data: Pick<PoolStock, 'instrument' | 'instrument_name'>) {
    return http<PoolStock>(`${this.base}/info`, {
      method: 'POST',
      data,
    });
  }

  static delete(id: number) {
    return http<void>(`${this.base}/info`, {
      method: 'DELETE',
      params: {
        id,
      },
    });
  }

  static sendInstruments(data: string[]) {
    return http<void>(`http://localhost:7670/instruments`, {
      method: 'POST',
      data,
    });
  }

  static clear() {
    return http<void>(`${this.base}/clear`);
  }
}

export default PoolService;
