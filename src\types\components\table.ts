import type { Column } from 'element-plus';
import type { JSX } from 'vue/jsx-runtime';
/** 表格列定义 */
export type ColumnDefinition<T> = {
  [K in keyof T]: Column<T[K]> & {
    key: K;
    dataKey?: K;
    show?: boolean;
    cellRenderer?: (props: {
      cellData: T[K];
      column: Column<T[K]>;
      rowData: T;
      rowIndex: number;
      columnIndex: number;
      columns: Column<T[K]>[];
    }) => JSX.Element | string | number;
  };
}[keyof T][];

/** 行操作 */
export interface RowAction<T> {
  /** 操作名称 */
  label: string;
  /** 点击事件 */
  onClick: (rowData: T) => void;
  /** 是否显示 */
  show?: (rowData: T) => boolean;
  /** 是否禁用 */
  disabled?: (rowData: T) => boolean;
  /** 按钮颜色 */
  color?: string;
}
