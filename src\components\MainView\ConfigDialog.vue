<script setup lang="ts">
import { ref, watch } from 'vue';
import { ElMessage } from 'element-plus';
import AutoConfigService from '@/api/autoConfig';
import type { AutoConfigSetting } from '@/types';

const visible = defineModel<boolean>();

// 默认配置
const DEFAULT_SETTING: AutoConfigSetting = {
  auto_start: false,
};

// 表单配置
const form = ref<AutoConfigSetting>({ ...DEFAULT_SETTING });
const id = ref(0);
const loading = ref(false);

// 监听弹窗显示状态
watch(
  () => visible.value,
  val => val && fetchConfig(),
);

// 查询配置
const fetchConfig = async () => {
  try {
    loading.value = true;
    const { error_msg, error_code, data } = await AutoConfigService.get();

    if (error_code === 0 && data) {
      id.value = data.id;
      const setting = JSON.parse(data.setting);
      form.value = {
        ...DEFAULT_SETTING,
        ...setting,
      };
    } else {
      ElMessage.error(error_msg || '获取配置失败');
    }
  } catch (error) {
    console.error('获取配置失败:', error);
    ElMessage.error('获取配置失败');
  } finally {
    loading.value = false;
  }
};

// 提交配置
const handleSubmit = async () => {
  loading.value = true;
  const settingStr = JSON.stringify(form.value);

  const { error_code, error_msg } = await AutoConfigService.update({
    id: id.value,
    setting: settingStr,
  });
  if (error_code === 0) {
    ElMessage.success('保存成功');
  } else {
    ElMessage.error(error_msg || '保存失败');
  }
  visible.value = false;
  loading.value = false;
};
</script>

<template>
  <el-dialog v-model="visible" title="修改配置" width="400px">
    <el-form ref="formRef" :model="form">
      <el-form-item label="自动启动">
        <div flex items-center>
          <el-switch v-model="form.auto_start" />
          <el-tooltip content="每个交易日9:30自动启动" placement="top">
            <i ml-5 i-mdi-help-circle-outline c-gray-400 />
          </el-tooltip>
        </div>
      </el-form-item>
    </el-form>

    <template #footer>
      <el-button size="small" @click="visible = false">取消</el-button>
      <el-button size="small" type="primary" :disabled="loading" @click="handleSubmit">
        保存
      </el-button>
    </template>
  </el-dialog>
</template>
