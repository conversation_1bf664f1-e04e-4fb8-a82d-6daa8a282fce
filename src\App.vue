<script setup lang="ts">
import { computed, defineAsyncComponent } from 'vue';
import { RouterView } from 'vue-router';
import { Misc } from '@/script';
import router from '@/router';
const AppHeader = defineAsyncComponent(() => import('./components/electron/AppHeader.vue'));

const showHeader = computed(() => {
  return Misc.isElectron() && router.currentRoute.value.name !== 'floating';
});
</script>

<template>
  <AppHeader v-if="showHeader" />
  <RouterView flex-1 min-h-1 />
</template>

<style scoped></style>
