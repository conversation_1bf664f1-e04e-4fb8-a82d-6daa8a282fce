<script setup lang="ts">
import { ref, useTemplateRef } from 'vue';
import { ElMessage, ElForm } from 'element-plus';
import { UserService } from '@/api';
import router from '@/router';
import { useUserStore } from '@/stores';
import { Misc } from '@/script';
const { setUserInfo } = useUserStore();

const rules = {
  username: [{ required: true, message: '请输入用户名', trigger: 'blur' }],
  password: [{ required: true, message: '请输入密码', trigger: 'blur' }],
  server: [{ required: true, message: '请选择服务器', trigger: 'change' }],
};

const loading = ref(false);

const form = ref({
  username: '',
  password: '',
  server: '',
});

const formRef = useTemplateRef('formRef');

const login = () => {
  if (!formRef.value) return;
  formRef.value.validate(async valid => {
    if (valid) {
      try {
        loading.value = true;
        const { error_code, error_msg, data } = await UserService.login({
          user_name: form.value.username,
          password: form.value.password,
        });

        if (error_code === 0) {
          setUserInfo(data);
          // 如何是electron环境，最大化
          if (Misc.isElectron()) {
            window.ipcRenderer.send('window-maximize', true);
          }
          router.push({ name: 'main' });
        } else {
          ElMessage.error(error_msg || '登录失败');
        }
      } finally {
        loading.value = false;
      }
    }
  });
};
</script>

<template>
  <div class="login-view" m30 flex justify-center aic>
    <div class="login-box" p-20 b-rd-8 w-300 flex flex-col aic jcc bg="[--g-panel-bg]">
      <el-form w-full ref="formRef" :model="form" :rules="rules" label-width="60px">
        <el-form-item label="用户名" prop="username">
          <el-input placeholder="请输入用户名" v-model="form.username" clearable />
        </el-form-item>
        <el-form-item label="密码" prop="password">
          <el-input placeholder="请输入密码" v-model="form.password" type="password" clearable />
        </el-form-item>
        <el-form-item>
          <el-button
            size="small"
            class="login-button"
            type="primary"
            :disabled="loading"
            @click="login"
          >
            登录
          </el-button>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<style scoped></style>
